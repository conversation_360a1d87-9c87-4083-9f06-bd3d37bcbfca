from dataclasses import dataclass

import requests
from loguru import logger

from core.settings import get_settings

INVALID_ACCESS_TOKEN = 40014
EXPIRED_ACCESS_TOKEN = 42001


@dataclass
class WecomService:
  corp_id: str
  corp_secret: str
  agent_id: str
  access_token: str | None = None
  base_url: str = "https://qyapi.weixin.qq.com/cgi-bin"
  _http = requests.Session()

  def __post_init__(self):
    self._fetch_access_token()

  def authorize_url(self, redirect_url: str, state: str = "LOGIN") -> str:
    return f"https://open.weixin.qq.com/connect/oauth2/authorize?appid={self.corp_id}&agentid={self.agent_id}&redirect_uri={redirect_url}&state={state}&response_type=code&scope=snsapi_privateinfo#wechat_redirect"

  def get_user_info(self, code: str):
    return self._get("auth/getuserinfo", params={"code": code})

  def get_user_detail(self, user_ticket: str):
    return self._get("auth/getuserdetail", json={"user_ticket": user_ticket})

  def _get(self, url, **kwargs):
    return self._request("GET", url, **kwargs)

  def _post(self, url, **kwargs):
    return self._request("POST", url, **kwargs)

  def _request(self, method: str, url: str, **kwargs):
    kwargs.setdefault("params", {})["access_token"] = self.access_token
    res = self._http.request(method, f"{self.base_url}/{url}", **kwargs)
    try:
      res.raise_for_status()
    except requests.exceptions.HTTPError as e:
      logger.error(f"请求失败: {e}")
      raise e
    return self._handle_result(res, method, url, **kwargs)

  def _handle_result(self, res: requests.Response, method: str, url: str, **kwargs):
    result = res.json()
    if errcode := result.get("errcode"):
      if errcode in [INVALID_ACCESS_TOKEN, EXPIRED_ACCESS_TOKEN]:
        self._fetch_access_token()
        return self._request(method, url, **kwargs)
      raise ValueError(f"Err Code: {result['errcode']} {result['errmsg']}")
    return result

  def _fetch_access_token(self):
    logger.info("获取 Access Token")
    res = self._http.get(
      f"{self.base_url}/gettoken",
      params={"corpid": self.corp_id, "corpsecret": self.corp_secret},
    )
    try:
      res.raise_for_status()
    except requests.exceptions.HTTPError as e:
      logger.error(f"获取 Access Token 失败: {e}")
      raise e
    self.access_token = res.json()["access_token"]


settings = get_settings()
wecom_service = WecomService(
  corp_id=settings.wecom_corp_id,
  corp_secret=settings.wecom_corp_secret,
  agent_id=settings.wecom_agent_id,
)

if __name__ == "__main__":
  # 测试企业
  wecom = WecomService(
    corp_id="ww98e6f25c9311c20b",
    corp_secret="8hJ3FUQuZUfE2xeHjIJnRsWiThxKwwVgIM-2L8cJUE8",
    agent_id="1000002",
  )
  print(wecom.access_token)
