from fastapi import APIRouter, status

from core.deps import SessionDep, AuthDep
from services.wecom import wecom_service
from services.auth import auth_service
from schemas import LoginSchema, UserPublicSchema, ResetPasswordSchema


router = APIRouter()


@router.post("/login", status_code=status.HTTP_200_OK)
def login(payload: LoginSchema, session: SessionDep):
  return auth_service.login(session, payload)


@router.post("/logout")
def logout(_: AuthDep):
  return {"message": "logout"}


@router.get("/session", response_model=UserPublicSchema)
def session(current_user: AuthDep):
  return current_user


@router.get("/wecom/authorize_url")
def wecom_authorize_url():
  return wecom_service.authorize_url()


@router.post("/reset-password")
def reset_password(payload: ResetPasswordSchema, user: AuthDep, session: SessionDep):
  return auth_service.reset_password(session, user, payload.password)
