{"python.defaultInterpreterPath": "${workspaceFolder}/backend/.venv/bin/python", "editor.formatOnSave": true, "files.exclude": {"**/.venv": true, "**/__pycache__": true, "**/node_modules": true, "**/.ruff_cache": true}, "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": "on"}, "tailwindCSS.classAttributes": ["class", "ui"], "tailwindCSS.experimental.classRegex": [["ui:\\s*{([^)]*)\\s*}", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}